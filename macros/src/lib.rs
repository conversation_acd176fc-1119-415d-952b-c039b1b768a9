mod model;
use proc_macro::{Delimiter, Group, Ident, Literal, Punct, Spacing, TokenStream, TokenTree};

#[proc_macro_attribute]
pub fn gvk(attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut group = String::new();
    let mut version = String::new();
    let mut kind = String::new();
    let mut plural = String::new();
    let mut singular = String::new();

    // 解析属性参数
    let mut tokens = attr.into_iter().peekable();
    while let Some(token) = tokens.next() {
        match token {
            TokenTree::Ident(ident) => {
                let key = ident.to_string();

                // 检查等号
                if let Some(TokenTree::Punct(p)) = tokens.peek() {
                    if p.as_char() == '=' {
                        tokens.next(); // 消耗等号

                        // 获取值
                        if let Some(TokenTree::Literal(lit)) = tokens.next() {
                            let value = lit.to_string().trim_matches('"').to_string();

                            match key.as_str() {
                                "group" => group = value,
                                "version" => version = value,
                                "kind" => kind = value,
                                "plural" => plural = value,
                                "singular" => singular = value,
                                _ => panic!("未知参数: {}", key),
                            }
                            continue;
                        }
                    }
                }
                panic!("无效的属性语法");
            }
            TokenTree::Punct(punct) => {
                // 跳过逗号分隔符
                if punct.as_char() == ',' {
                    continue;
                }
                panic!("无效的属性语法");
            }
            _ => {
                panic!("无效的属性语法");
            }
        }
    }

    // 确保所有必需参数都存在
    assert!(!version.is_empty(), "缺少 version 参数");
    assert!(!kind.is_empty(), "缺少 kind 参数");
    assert!(!plural.is_empty(), "缺少 plural 参数");
    assert!(!singular.is_empty(), "缺少 singular 参数");

    // 解析原结构体定义
    let mut item_iter = item.into_iter();
    let mut struct_tokens = Vec::new();
    let mut struct_name = None;

    // 标记是否已进入结构体主体
    let mut in_struct_body = false;
    // 标记是否已添加自定义字段
    let mut fields_added = false;

    while let Some(token) = item_iter.next() {
        match &token {
            TokenTree::Ident(ident) => {
                // 检测 struct 关键字
                if ident.to_string() == "struct" {
                    struct_tokens.push(token.clone());

                    // 获取结构体名称
                    if let Some(TokenTree::Ident(name)) = item_iter.next() {
                        struct_name = Some(name.clone());
                        struct_tokens.push(TokenTree::Ident(name));
                        continue;
                    }
                }
            }
            TokenTree::Group(group) if group.delimiter() == Delimiter::Brace => {
                // 如果遇到结构体主体，添加我们的自定义字段
                if !fields_added {
                    in_struct_body = true;

                    // 构建新字段
                    let mut new_fields = vec![
                        build_field(
                            "api_version",
                            "String",
                            Some(vec!["#[serde(rename = \"apiVersion\")]"]),
                        ),
                        build_field("kind", "String", None),
                        build_field("metadata", "Metadata", None),
                    ];

                    // 添加原始字段
                    if let Some(content) = parse_group_content(&group) {
                        new_fields.extend(content);
                    }

                    // 创建带新字段的结构体主体
                    let new_body = Group::new(Delimiter::Brace, TokenStream::from_iter(new_fields));

                    struct_tokens.push(TokenTree::Group(new_body));
                    fields_added = true;
                    continue;
                }
            }
            _ => {}
        }

        if !in_struct_body {
            struct_tokens.push(token.clone());
        }
    }

    let struct_name = struct_name.expect("无法找到结构体名称");
    // 创建 API 版本字符串
    let api_version = if group.is_empty() {
        version.clone()
    } else {
        format!("{}/{}", group, version)
    };

    // 构建生成的实现
    let mut output = Vec::new();
    output.extend(struct_tokens);

    // 实现 GVK trait
    let gvk_impl = build_trait_impl(
        "halo_model::gvk::GVK",
        struct_name.clone(),
        vec![
            ("group", group),
            ("version", version),
            ("kind", kind.clone()),
            ("plural", plural),
            ("singular", singular),
            ("api_version", api_version.clone()),
        ],
    );
    output.extend(gvk_impl);

    // 实现 ExtensionOperator trait
    let mut ext_impl = vec![
        TokenTree::Ident(Ident::new("impl", proc_macro::Span::call_site().into())),
    ];

    // 解析 trait 路径
    ext_impl.extend(parse_trait_path("halo_model::extension::ExtensionOperator"));

    ext_impl.extend(vec![
        TokenTree::Ident(Ident::new("for", proc_macro::Span::call_site().into())),
        TokenTree::Ident(struct_name.clone()),
        TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
            // api_version() 方法 - 优先使用静态值，否则使用字段值
            build_dynamic_method("api_version", api_version.as_str(), "api_version"),
            // kind() 方法 - 优先使用静态值，否则使用字段值
            build_dynamic_method("kind", kind.as_str(), "kind"),
            // metadata() 方法
            build_field_method("metadata", "&dyn MetadataOperator", "metadata"),
            // metadata_mut() 方法
            build_field_method("metadata_mut", "&mut dyn MetadataOperator", "metadata"),
        ]))),
    ]);
    output.extend(ext_impl);

    TokenStream::from_iter(output)
}

// 解析 trait 路径的辅助函数
fn parse_trait_path(trait_name: &str) -> Vec<TokenTree> {
    let mut tokens = Vec::new();
    let parts: Vec<&str> = trait_name.split("::").collect();

    for (i, part) in parts.iter().enumerate() {
        if i > 0 {
            // 添加 :: 分隔符
            tokens.push(TokenTree::Punct(Punct::new(':', Spacing::Joint)));
            tokens.push(TokenTree::Punct(Punct::new(':', Spacing::Alone)));
        }
        tokens.push(TokenTree::Ident(Ident::new(part, proc_macro::Span::call_site().into())));
    }

    tokens
}

// 解析类型的辅助函数
fn parse_type(type_str: &str) -> Vec<TokenTree> {
    let mut tokens = Vec::new();
    let mut chars = type_str.chars().peekable();

    while let Some(ch) = chars.next() {
        match ch {
            '&' => {
                tokens.push(TokenTree::Punct(Punct::new('&', Spacing::Alone)));
            }
            ' ' => {
                // 跳过空格
                continue;
            }
            _ => {
                // 收集标识符
                let mut ident = String::new();
                ident.push(ch);

                while let Some(&next_ch) = chars.peek() {
                    if next_ch.is_alphanumeric() || next_ch == '_' {
                        ident.push(chars.next().unwrap());
                    } else {
                        break;
                    }
                }

                tokens.push(TokenTree::Ident(Ident::new(&ident, proc_macro::Span::call_site().into())));
            }
        }
    }

    tokens
}

fn build_trait_impl(trait_name: &str, struct_name: Ident, methods: Vec<(&str, String)>) -> Vec<TokenTree> {
    let mut tokens = vec![
        TokenTree::Ident(Ident::new("impl", proc_macro::Span::call_site().into())),
    ];

    // 解析 trait 路径
    tokens.extend(parse_trait_path(trait_name));

    tokens.extend(vec![
        TokenTree::Ident(Ident::new("for", proc_macro::Span::call_site().into())),
        TokenTree::Ident(struct_name),
    ]);

    let method_tokens: Vec<TokenTree> = methods.into_iter().map(|(name, value)| {
        TokenTree::Group(Group::new(Delimiter::None, TokenStream::from_iter(vec![
            TokenTree::Ident(Ident::new("fn", proc_macro::Span::call_site().into())),
            TokenTree::Ident(Ident::new(name, proc_macro::Span::call_site().into())),
            TokenTree::Group(Group::new(Delimiter::Parenthesis, TokenStream::new())),
            TokenTree::Punct(Punct::new('-', Spacing::Alone)),
            TokenTree::Punct(Punct::new('>', Spacing::Alone)),
            TokenTree::Punct(Punct::new('&', Spacing::Alone)),
            TokenTree::Punct(Punct::new('\'', Spacing::Alone)),
            TokenTree::Ident(Ident::new("static", proc_macro::Span::call_site().into())),
            TokenTree::Ident(Ident::new("str", proc_macro::Span::call_site().into())),
            TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
                TokenTree::Literal(Literal::string(&value))
            ])))
        ])))
    }).collect();

    tokens.push(TokenTree::Group(Group::new(
        Delimiter::Brace,
        TokenStream::from_iter(method_tokens),
    )));

    tokens
}

// 构建带有字段回退逻辑的方法
fn build_dynamic_method(name: &str, static_value: &str, field_name: &str) -> TokenTree {
    Group::new(Delimiter::None, TokenStream::from_iter(vec![
        TokenTree::Ident(Ident::new("fn", proc_macro::Span::call_site().into())),
        TokenTree::Ident(Ident::new(name, proc_macro::Span::call_site().into())),
        TokenTree::Group(Group::new(Delimiter::Parenthesis, TokenStream::from_iter(vec![
            TokenTree::Punct(Punct::new('&', Spacing::Alone)),
            TokenTree::Ident(Ident::new("self", proc_macro::Span::call_site().into())),
        ]))),
        TokenTree::Punct(Punct::new('-', Spacing::Alone)),
        TokenTree::Punct(Punct::new('>', Spacing::Alone)),
        TokenTree::Punct(Punct::new('&', Spacing::Alone)),
        TokenTree::Ident(Ident::new("str", proc_macro::Span::call_site().into())),
        TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
            TokenTree::Ident(Ident::new("let", proc_macro::Span::call_site().into())),
            TokenTree::Ident(Ident::new("static_value", proc_macro::Span::call_site().into())),
            TokenTree::Punct(Punct::new('=', Spacing::Alone)),
            TokenTree::Literal(Literal::string(static_value)),
            TokenTree::Punct(Punct::new(';', Spacing::Alone)),
            TokenTree::Ident(Ident::new("if", proc_macro::Span::call_site().into())),
            TokenTree::Punct(Punct::new('!', Spacing::Alone)),
            TokenTree::Ident(Ident::new("static_value", proc_macro::Span::call_site().into())),
            TokenTree::Punct(Punct::new('.', Spacing::Alone)),
            TokenTree::Ident(Ident::new("is_empty", proc_macro::Span::call_site().into())),
            TokenTree::Punct(Punct::new('(', Spacing::Alone)),
            TokenTree::Punct(Punct::new(')', Spacing::Alone)),
            TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
                TokenTree::Ident(Ident::new("static_value", proc_macro::Span::call_site().into())),
            ]))),
            TokenTree::Ident(Ident::new("else", proc_macro::Span::call_site().into())),
            TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
                TokenTree::Punct(Punct::new('&', Spacing::Alone)),
                TokenTree::Ident(Ident::new("self", proc_macro::Span::call_site().into())),
                TokenTree::Punct(Punct::new('.', Spacing::Alone)),
                TokenTree::Ident(Ident::new(field_name, proc_macro::Span::call_site().into())),
            ]))),
        ]))),
    ])).into()
}
// 解析组内容的辅助函数
fn parse_group_content(group: &Group) -> Option<Vec<TokenTree>> {
    if group.delimiter() == Delimiter::Brace {
        let mut content = Vec::new();
        let mut stream = group.stream().into_iter();
        while let Some(token) = stream.next() {
            content.push(token);
        }
        Some(content)
    } else {
        None
    }
}

// 构建字段定义的辅助函数
fn build_field(name: &str, field_type: &str, attributes: Option<Vec<&str>>) -> TokenTree {
    let mut tokens = Vec::new();

    // 添加属性
    if let Some(attrs) = attributes {
        for attr in attrs {
            tokens.push(TokenTree::Punct(Punct::new('#', Spacing::Alone)));
            tokens.push(TokenTree::Group(Group::new(
                Delimiter::Bracket,
                attr.parse().unwrap(),
            )));
        }
    }

    // 添加字段名称
    tokens.push(TokenTree::Ident(Ident::new(
        name,
        proc_macro::Span::call_site().into(),
    )));
    tokens.push(TokenTree::Punct(Punct::new(':', Spacing::Alone)));

    // 添加字段类型
    tokens.push(TokenTree::Ident(Ident::new(
        field_type,
        proc_macro::Span::call_site().into(),
    )));
    tokens.push(TokenTree::Punct(Punct::new(',', Spacing::Alone)));

    TokenTree::Group(Group::new(Delimiter::None, TokenStream::from_iter(tokens)))
}
// 构建字段访问方法
fn build_field_method(name: &str, return_type: &str, field_name: &str) -> TokenTree {
    let mut tokens = vec![
        TokenTree::Ident(Ident::new("fn", proc_macro::Span::call_site().into())),
        TokenTree::Ident(Ident::new(name, proc_macro::Span::call_site().into())),
        TokenTree::Group(Group::new(Delimiter::Parenthesis, TokenStream::from_iter(vec![
            TokenTree::Punct(Punct::new('&', Spacing::Alone)),
            TokenTree::Ident(Ident::new("self", proc_macro::Span::call_site().into())),
        ]))),
        TokenTree::Punct(Punct::new('-', Spacing::Alone)),
        TokenTree::Punct(Punct::new('>', Spacing::Alone)),
    ];

    // 解析返回类型
    tokens.extend(parse_type(return_type));

    tokens.push(TokenTree::Group(Group::new(Delimiter::Brace, TokenStream::from_iter(vec![
        TokenTree::Punct(Punct::new('&', Spacing::Alone)),
        TokenTree::Ident(Ident::new("self", proc_macro::Span::call_site().into())),
        TokenTree::Punct(Punct::new('.', Spacing::Alone)),
        TokenTree::Ident(Ident::new(field_name, proc_macro::Span::call_site().into())),
    ]))));

    Group::new(Delimiter::None, TokenStream::from_iter(tokens)).into()
}


#[cfg(test)]
mod tests {
    use super::*;
    use proc_macro2::TokenStream;
    use quote::quote;
    use syn::ItemStruct;

    #[test]
    fn test_gvk_macro_expansion() {
        // 模拟输入
        let input: TokenStream = quote! {
            #[gvk(
                group = "test.group",
                version = "v1alpha1",
                kind = "TestResource",
                plural = "testresources",
                singular = "testresource"
            )]
            struct TestResource {
                value: i32,
            }
        };

        // 应用宏
        let output = gvk(quote! {}, input);

        // 解析为结构体项目
        let parsed = syn::parse2::<ItemStruct>(output).expect("Failed to parse output");

        // 检查添加的字段
        let mut has_api_version = false;
        let mut has_kind = false;
        let mut has_metadata = false;

        if let syn::Fields::Named(fields) = &parsed.fields {
            for field in &fields.named {
                if let Some(ident) = &field.ident {
                    match ident.to_string().as_str() {
                        "api_version" => has_api_version = true,
                        "kind" => has_kind = true,
                        "metadata" => has_metadata = true,
                        _ => {}
                    }
                }
            }
        }

        assert!(has_api_version, "Missing api_version field");
        assert!(has_kind, "Missing kind field");
        assert!(has_metadata, "Missing metadata field");

        // 检查生成的实现
        let output_str = output.to_string();
        assert!(output_str.contains("impl halo_model::gvk::GVK for TestResource"));
        assert!(output_str.contains("impl halo_model::extension::ExtensionOperator for TestResource"));
    }

    #[test]
    fn test_missing_parameters() {
        let input: TokenStream = quote! {
            #[gvk(
                group = "test.group",
                // 缺少 version
                kind = "TestResource"
            )]
            struct TestResource;
        };

        // 应用宏应该 panic
        let result = std::panic::catch_unwind(|| {
            gvk(quote! {}, input);
        });

        assert!(result.is_err(), "Macro should panic when missing required parameters");
    }

    #[test]
    fn test_empty_group() {
        let input: TokenStream = quote! {
            #[gvk(
                group = "",
                version = "v1alpha1",
                kind = "TestResource",
                plural = "testresources",
                singular = "testresource"
            )]
            struct TestResource;
        };

        // 应用宏
        let output = gvk(quote! {}, input);
        let output_str = output.to_string();

        // 验证 API 版本格式
        assert!(output_str.contains("fn api_version() -> &'static str { \"v1alpha1\" }"));
    }
}

