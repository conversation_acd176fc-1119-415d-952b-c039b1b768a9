use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::fmt;
use std::time::SystemTime;

// 元数据操作接口
pub trait MetadataOperator {
    fn name(&self) -> &str;
    fn set_name(&mut self, name: String);
    fn generate_name(&self) -> Option<&str>;
    fn set_generate_name(&mut self, name: Option<String>);
    fn labels(&self) -> &HashMap<String, String>;
    fn labels_mut(&mut self) -> &mut HashMap<String, String>;
    fn annotations(&self) -> &HashMap<String, String>;
    fn annotations_mut(&mut self) -> &mut HashMap<String, String>;
    fn version(&self) -> Option<&String>;
    fn set_version(&mut self, version: Option<String>);
    fn creation_timestamp(&self) -> Option<&SystemTime>;
    fn set_creation_timestamp(&mut self, timestamp: Option<SystemTime>);
    fn deletion_timestamp(&self) -> Option<&SystemTime>;
    fn set_deletion_timestamp(&mut self, timestamp: Option<SystemTime>);
    fn finalizers(&self) -> &HashSet<String>;
    fn finalizers_mut(&mut self) -> &mut HashSet<String>;
    fn is_being_deleted(&self) -> bool {
        self.deletion_timestamp().is_some()
    }
    fn is_not_deleted(&self) -> bool {
        !self.is_being_deleted()
    }
}

// 元数据结构实现
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
#[serde(default, rename_all = "camelCase")]
pub struct Metadata {
    pub name: String,
    pub generate_name: Option<String>,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
    pub version: Option<String>,
    pub creation_timestamp: Option<SystemTime>,
    pub deletion_timestamp: Option<SystemTime>,
    pub finalizers: HashSet<String>,
}

impl MetadataOperator for Metadata {
    fn name(&self) -> &str {
        &self.name
    }

    fn set_name(&mut self, name: String) {
        self.name = name;
    }

    fn generate_name(&self) -> Option<&str> {
        self.generate_name.as_deref()
    }

    fn set_generate_name(&mut self, name: Option<String>) {
        self.generate_name = name;
    }

    fn labels(&self) -> &HashMap<String, String> {
        &self.labels
    }

    fn labels_mut(&mut self) -> &mut HashMap<String, String> {
        &mut self.labels
    }

    fn annotations(&self) -> &HashMap<String, String> {
        &self.annotations
    }

    fn annotations_mut(&mut self) -> &mut HashMap<String, String> {
        &mut self.annotations
    }

    fn version(&self) -> Option<&String> {
        self.version.as_ref()
    }

    fn set_version(&mut self, version: Option<String>) {
        self.version = version;
    }

    fn creation_timestamp(&self) -> Option<&SystemTime> {
        self.creation_timestamp.as_ref()
    }

    fn set_creation_timestamp(&mut self, timestamp: Option<SystemTime>) {
        self.creation_timestamp = timestamp;
    }

    fn deletion_timestamp(&self) -> Option<&SystemTime> {
        self.deletion_timestamp.as_ref()
    }

    fn set_deletion_timestamp(&mut self, timestamp: Option<SystemTime>) {
        self.deletion_timestamp = timestamp;
    }

    fn finalizers(&self) -> &HashSet<String> {
        &self.finalizers
    }

    fn finalizers_mut(&mut self) -> &mut HashSet<String> {
        &mut self.finalizers
    }
}

impl fmt::Display for Metadata {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Metadata(name={}", self.name)?;

        if let Some(generate_name) = &self.generate_name {
            write!(f, ", generate_name={}", generate_name)?;
        }

        if !self.labels.is_empty() {
            write!(f, ", labels={:?}", self.labels)?;
        }

        if let Some(version) = &self.version {
            write!(f, ", version={}", version)?;
        }

        if let Some(deletion) = self.deletion_timestamp {
            write!(f, ", deletion_timestamp={:?}", deletion)?;
        }

        write!(f, ")")
    }
}